# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "c9d77d99620afe2f1f53dc8a502002b5"
name = "ProdRankX"
handle = "prodrankx"
application_url = "https://circuits-plaintiff-bedding-levels.trycloudflare.com"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-07"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

  # Billing webhooks for subscription management
  [[webhooks.subscriptions]]
  topics = [ "app_subscriptions/update" ]
  uri = "/webhooks/app_subscriptions/update"

  [[webhooks.subscriptions]]
  topics = [ "app_purchases_one_time/update" ]
  uri = "/webhooks/app_purchases_one_time/update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_products,write_products,read_orders,write_orders"

[auth]
redirect_urls = ["https://circuits-plaintiff-bedding-levels.trycloudflare.com/auth/callback", "https://circuits-plaintiff-bedding-levels.trycloudflare.com/auth/shopify/callback", "https://circuits-plaintiff-bedding-levels.trycloudflare.com/api/auth/callback"]

[pos]
embedded = false
