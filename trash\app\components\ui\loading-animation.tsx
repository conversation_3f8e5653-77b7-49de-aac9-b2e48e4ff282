import * as React from "react";
import { motion } from "framer-motion";

interface LoadingAnimationProps {
  message?: string;
  className?: string;
}

export function LoadingAnimation({ message = "Loading...", className = "" }: LoadingAnimationProps) {
  return (
    <div className={`flex flex-col items-center justify-center py-16 ${className}`}>
      {/* Animated Loading Dots */}
      <div className="flex space-x-2 mb-6">
        {[0, 1, 2].map((i) => (
          <motion.div
            key={i}
            className="w-4 h-4 bg-black rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              delay: i * 0.2,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>

      {/* Loading Message */}
      <motion.p
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="text-lg font-medium text-gray-600"
      >
        {message}
      </motion.p>

      {/* Progress Bar */}
      <div className="w-64 h-2 bg-gray-200 rounded-full mt-4 overflow-hidden">
        <motion.div
          className="h-full bg-black rounded-full"
          animate={{
            x: ["-100%", "100%"],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>
    </div>
  );
}

interface ProductLoadingSkeletonProps {
  count?: number;
}

export function ProductLoadingSkeletons({ count = 6 }: ProductLoadingSkeletonProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: i * 0.1 }}
          className="bg-white rounded-3xl p-6 border border-gray-200 shadow-lg"
        >
          {/* Product Image Skeleton */}
          <div className="w-full h-48 bg-gray-200 rounded-2xl mb-4 relative overflow-hidden">
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
              animate={{
                x: ["-100%", "100%"],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </div>

          {/* Product Title Skeleton */}
          <div className="h-6 bg-gray-200 rounded-full mb-3 relative overflow-hidden">
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
              animate={{
                x: ["-100%", "100%"],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: 0.2,
                ease: "easeInOut"
              }}
            />
          </div>

          {/* Product Description Skeleton */}
          <div className="space-y-2 mb-4">
            <div className="h-4 bg-gray-200 rounded-full relative overflow-hidden">
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
                animate={{
                  x: ["-100%", "100%"],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: 0.4,
                  ease: "easeInOut"
                }}
              />
            </div>
            <div className="h-4 bg-gray-200 rounded-full w-3/4 relative overflow-hidden">
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
                animate={{
                  x: ["-100%", "100%"],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: 0.6,
                  ease: "easeInOut"
                }}
              />
            </div>
          </div>

          {/* SEO Score Skeleton */}
          <div className="flex items-center justify-between">
            <div className="h-8 w-20 bg-gray-200 rounded-full relative overflow-hidden">
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
                animate={{
                  x: ["-100%", "100%"],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: 0.8,
                  ease: "easeInOut"
                }}
              />
            </div>
            <div className="h-10 w-24 bg-gray-200 rounded-full relative overflow-hidden">
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
                animate={{
                  x: ["-100%", "100%"],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: 1,
                  ease: "easeInOut"
                }}
              />
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
}

interface MetricSkeletonProps {
  count?: number;
}

export function MetricSkeletons({ count = 4 }: MetricSkeletonProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: i * 0.1 }}
          className="bg-white rounded-3xl p-6 border border-gray-200 shadow-lg"
        >
          {/* Icon Skeleton */}
          <div className="w-12 h-12 bg-gray-200 rounded-2xl mb-4 relative overflow-hidden">
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
              animate={{
                x: ["-100%", "100%"],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.2,
                ease: "easeInOut"
              }}
            />
          </div>

          {/* Value Skeleton */}
          <div className="h-8 bg-gray-200 rounded-full mb-2 relative overflow-hidden">
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
              animate={{
                x: ["-100%", "100%"],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.2 + 0.3,
                ease: "easeInOut"
              }}
            />
          </div>

          {/* Label Skeleton */}
          <div className="h-4 bg-gray-200 rounded-full w-2/3 relative overflow-hidden">
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
              animate={{
                x: ["-100%", "100%"],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.2 + 0.6,
                ease: "easeInOut"
              }}
            />
          </div>
        </motion.div>
      ))}
    </div>
  );
}
