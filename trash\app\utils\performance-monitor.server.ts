/**
 * Performance Monitoring and Metrics Collection
 * Tracks API calls, response times, and system performance
 */

interface PerformanceMetric {
  operation: string;
  shop: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  success: boolean;
  error?: string;
  metadata?: Record<string, any>;
}

interface PerformanceStats {
  totalOperations: number;
  successRate: number;
  averageResponseTime: number;
  slowestOperation: number;
  fastestOperation: number;
  errorCount: number;
  operationBreakdown: Record<string, {
    count: number;
    averageTime: number;
    successRate: number;
  }>;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private activeOperations = new Map<string, PerformanceMetric>();
  private maxMetrics = 1000; // Keep last 1000 metrics

  /**
   * Start tracking an operation
   */
  startOperation(operation: string, shop: string, metadata?: Record<string, any>): string {
    const operationId = `${operation}_${shop}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const metric: PerformanceMetric = {
      operation,
      shop,
      startTime: Date.now(),
      success: false,
      metadata
    };

    this.activeOperations.set(operationId, metric);
    
    console.log(`📊 Started tracking: ${operation} for shop ${shop}`);
    return operationId;
  }

  /**
   * End tracking an operation
   */
  endOperation(operationId: string, success: boolean = true, error?: string): void {
    const metric = this.activeOperations.get(operationId);
    if (!metric) {
      console.warn(`⚠️ No active operation found for ID: ${operationId}`);
      return;
    }

    metric.endTime = Date.now();
    metric.duration = metric.endTime - metric.startTime;
    metric.success = success;
    metric.error = error;

    // Move to completed metrics
    this.metrics.push(metric);
    this.activeOperations.delete(operationId);

    // Keep only the most recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    console.log(`📊 Completed tracking: ${metric.operation} for shop ${metric.shop} (${metric.duration}ms, success: ${success})`);
  }

  /**
   * Get performance statistics
   */
  getStats(timeWindowMs?: number): PerformanceStats {
    const now = Date.now();
    const relevantMetrics = timeWindowMs 
      ? this.metrics.filter(m => m.endTime && (now - m.endTime) <= timeWindowMs)
      : this.metrics;

    if (relevantMetrics.length === 0) {
      return {
        totalOperations: 0,
        successRate: 0,
        averageResponseTime: 0,
        slowestOperation: 0,
        fastestOperation: 0,
        errorCount: 0,
        operationBreakdown: {}
      };
    }

    const successfulOps = relevantMetrics.filter(m => m.success);
    const durations = relevantMetrics.map(m => m.duration || 0).filter(d => d > 0);
    
    const operationBreakdown: Record<string, { count: number; averageTime: number; successRate: number }> = {};
    
    // Calculate breakdown by operation type
    for (const metric of relevantMetrics) {
      if (!operationBreakdown[metric.operation]) {
        operationBreakdown[metric.operation] = {
          count: 0,
          averageTime: 0,
          successRate: 0
        };
      }
      
      operationBreakdown[metric.operation].count++;
    }

    // Calculate averages and success rates
    for (const [operation, stats] of Object.entries(operationBreakdown)) {
      const operationMetrics = relevantMetrics.filter(m => m.operation === operation);
      const operationDurations = operationMetrics.map(m => m.duration || 0).filter(d => d > 0);
      const successfulOperations = operationMetrics.filter(m => m.success);

      stats.averageTime = operationDurations.length > 0 
        ? operationDurations.reduce((sum, d) => sum + d, 0) / operationDurations.length
        : 0;
      
      stats.successRate = operationMetrics.length > 0 
        ? (successfulOperations.length / operationMetrics.length) * 100
        : 0;
    }

    return {
      totalOperations: relevantMetrics.length,
      successRate: relevantMetrics.length > 0 ? (successfulOps.length / relevantMetrics.length) * 100 : 0,
      averageResponseTime: durations.length > 0 ? durations.reduce((sum, d) => sum + d, 0) / durations.length : 0,
      slowestOperation: durations.length > 0 ? Math.max(...durations) : 0,
      fastestOperation: durations.length > 0 ? Math.min(...durations) : 0,
      errorCount: relevantMetrics.filter(m => !m.success).length,
      operationBreakdown
    };
  }

  /**
   * Get metrics for a specific shop
   */
  getShopStats(shop: string, timeWindowMs?: number): PerformanceStats {
    const now = Date.now();
    const shopMetrics = this.metrics.filter(m => {
      const matchesShop = m.shop === shop;
      const withinTimeWindow = !timeWindowMs || (m.endTime && (now - m.endTime) <= timeWindowMs);
      return matchesShop && withinTimeWindow;
    });

    // Use the same calculation logic but with filtered metrics
    const originalMetrics = this.metrics;
    this.metrics = shopMetrics;
    const stats = this.getStats();
    this.metrics = originalMetrics;

    return stats;
  }

  /**
   * Get slow operations (above threshold)
   */
  getSlowOperations(thresholdMs: number = 5000, limit: number = 10): PerformanceMetric[] {
    return this.metrics
      .filter(m => m.duration && m.duration > thresholdMs)
      .sort((a, b) => (b.duration || 0) - (a.duration || 0))
      .slice(0, limit);
  }

  /**
   * Get failed operations
   */
  getFailedOperations(limit: number = 10): PerformanceMetric[] {
    return this.metrics
      .filter(m => !m.success)
      .sort((a, b) => (b.startTime || 0) - (a.startTime || 0))
      .slice(0, limit);
  }

  /**
   * Clear old metrics
   */
  clearOldMetrics(olderThanMs: number): void {
    const cutoff = Date.now() - olderThanMs;
    const originalLength = this.metrics.length;
    
    this.metrics = this.metrics.filter(m => m.endTime && m.endTime > cutoff);
    
    const removed = originalLength - this.metrics.length;
    if (removed > 0) {
      console.log(`📊 Cleared ${removed} old performance metrics`);
    }
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = ['operation', 'shop', 'startTime', 'endTime', 'duration', 'success', 'error'];
      const rows = this.metrics.map(m => [
        m.operation,
        m.shop,
        m.startTime,
        m.endTime || '',
        m.duration || '',
        m.success,
        m.error || ''
      ]);
      
      return [headers, ...rows].map(row => row.join(',')).join('\n');
    }

    return JSON.stringify(this.metrics, null, 2);
  }
}

// Global performance monitor instance
const performanceMonitor = new PerformanceMonitor();

/**
 * Decorator function to automatically track method performance
 */
export function trackPerformance(operation: string) {
  return function (_target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (this: any, ...args: any[]) {
      const shop = this.shop || 'unknown';
      const operationId = performanceMonitor.startOperation(operation, shop, {
        method: propertyName,
        args: args.length
      });

      try {
        const result = await method.apply(this, args);
        performanceMonitor.endOperation(operationId, true);
        return result;
      } catch (error) {
        performanceMonitor.endOperation(operationId, false, error instanceof Error ? error.message : String(error));
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Manual performance tracking functions
 */
export function startPerformanceTracking(operation: string, shop: string, metadata?: Record<string, any>): string {
  return performanceMonitor.startOperation(operation, shop, metadata);
}

export function endPerformanceTracking(operationId: string, success: boolean = true, error?: string): void {
  performanceMonitor.endOperation(operationId, success, error);
}

/**
 * Get performance statistics
 */
export function getPerformanceStats(timeWindowMs?: number): PerformanceStats {
  return performanceMonitor.getStats(timeWindowMs);
}

export function getShopPerformanceStats(shop: string, timeWindowMs?: number): PerformanceStats {
  return performanceMonitor.getShopStats(shop, timeWindowMs);
}

/**
 * Get problematic operations
 */
export function getSlowOperations(thresholdMs: number = 5000, limit: number = 10): PerformanceMetric[] {
  return performanceMonitor.getSlowOperations(thresholdMs, limit);
}

export function getFailedOperations(limit: number = 10): PerformanceMetric[] {
  return performanceMonitor.getFailedOperations(limit);
}

/**
 * Maintenance functions
 */
export function clearOldPerformanceMetrics(olderThanMs: number = 24 * 60 * 60 * 1000): void {
  performanceMonitor.clearOldMetrics(olderThanMs);
}

export function exportPerformanceMetrics(format: 'json' | 'csv' = 'json'): string {
  return performanceMonitor.exportMetrics(format);
}

/**
 * Performance monitoring middleware for billing operations
 */
export async function withPerformanceTracking<T>(
  operation: string,
  shop: string,
  fn: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  const operationId = startPerformanceTracking(operation, shop, metadata);
  
  try {
    const result = await fn();
    endPerformanceTracking(operationId, true);
    return result;
  } catch (error) {
    endPerformanceTracking(operationId, false, error instanceof Error ? error.message : String(error));
    throw error;
  }
}

/**
 * Log performance summary
 */
export function logPerformanceSummary(timeWindowMs: number = 60 * 60 * 1000): void {
  const stats = getPerformanceStats(timeWindowMs);
  
  console.log(`📊 Performance Summary (last ${timeWindowMs / 1000 / 60} minutes):`);
  console.log(`   Total Operations: ${stats.totalOperations}`);
  console.log(`   Success Rate: ${stats.successRate.toFixed(1)}%`);
  console.log(`   Average Response Time: ${stats.averageResponseTime.toFixed(0)}ms`);
  console.log(`   Slowest Operation: ${stats.slowestOperation}ms`);
  console.log(`   Error Count: ${stats.errorCount}`);
  
  if (Object.keys(stats.operationBreakdown).length > 0) {
    console.log(`   Operation Breakdown:`);
    for (const [operation, breakdown] of Object.entries(stats.operationBreakdown)) {
      console.log(`     ${operation}: ${breakdown.count} ops, ${breakdown.averageTime.toFixed(0)}ms avg, ${breakdown.successRate.toFixed(1)}% success`);
    }
  }
}

// Clean up old metrics every hour
setInterval(() => {
  clearOldPerformanceMetrics();
}, 60 * 60 * 1000);

// Log performance summary every 30 minutes
setInterval(() => {
  logPerformanceSummary();
}, 30 * 60 * 1000);

export default performanceMonitor;
