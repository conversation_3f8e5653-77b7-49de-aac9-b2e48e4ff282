/**
 * User-Friendly Messages Utility
 * Converts technical errors into user-friendly messages
 */

export interface UserMessage {
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  action?: {
    label: string;
    onClick: () => void;
  };
}

/**
 * Convert billing errors to user-friendly messages
 */
export function getBillingErrorMessage(error: string): UserMessage {
  const lowerError = error.toLowerCase();

  // Authentication errors
  if (lowerError.includes('authentication') || lowerError.includes('unauthorized')) {
    return {
      type: 'error',
      title: 'Authentication Required',
      message: 'Please refresh the page and try again. If the problem persists, contact support.',
      action: {
        label: 'Refresh Page',
        onClick: () => window.location.reload()
      }
    };
  }

  // Rate limiting errors
  if (lowerError.includes('rate limit') || lowerError.includes('too many requests')) {
    return {
      type: 'warning',
      title: 'Too Many Requests',
      message: 'Please wait a moment before trying again. We limit requests to ensure system stability.',
      action: {
        label: 'Try Again',
        onClick: () => window.location.reload()
      }
    };
  }

  // CSRF errors
  if (lowerError.includes('csrf') || lowerError.includes('security token')) {
    return {
      type: 'error',
      title: 'Security Check Failed',
      message: 'For your security, please refresh the page and try again.',
      action: {
        label: 'Refresh Page',
        onClick: () => window.location.reload()
      }
    };
  }

  // Validation errors
  if (lowerError.includes('validation') || lowerError.includes('invalid')) {
    return {
      type: 'error',
      title: 'Invalid Information',
      message: 'Please check your input and try again. Make sure all required fields are filled correctly.',
    };
  }

  // Plan-specific errors
  if (lowerError.includes('plan') && lowerError.includes('not found')) {
    return {
      type: 'error',
      title: 'Plan Not Available',
      message: 'The selected plan is no longer available. Please choose a different plan.',
      action: {
        label: 'View Plans',
        onClick: () => window.location.href = '/app/billing/pricing'
      }
    };
  }

  // Subscription errors
  if (lowerError.includes('subscription')) {
    if (lowerError.includes('already exists') || lowerError.includes('duplicate')) {
      return {
        type: 'warning',
        title: 'Already Subscribed',
        message: 'You already have an active subscription. Check your billing dashboard for details.',
        action: {
          label: 'View Dashboard',
          onClick: () => window.location.href = '/app/billing'
        }
      };
    }

    if (lowerError.includes('cancelled') || lowerError.includes('expired')) {
      return {
        type: 'error',
        title: 'Subscription Issue',
        message: 'There was an issue with your subscription. Please try subscribing again.',
        action: {
          label: 'Try Again',
          onClick: () => window.location.href = '/app/billing/pricing'
        }
      };
    }
  }

  // Payment errors
  if (lowerError.includes('payment') || lowerError.includes('billing') || lowerError.includes('charge')) {
    return {
      type: 'error',
      title: 'Payment Issue',
      message: 'There was a problem processing your payment. Please check your payment method and try again.',
      action: {
        label: 'Try Again',
        onClick: () => window.location.reload()
      }
    };
  }

  // Product count errors
  if (lowerError.includes('product count')) {
    if (lowerError.includes('exceed') || lowerError.includes('maximum')) {
      return {
        type: 'warning',
        title: 'Too Many Products',
        message: 'You can optimize up to 1,000 products per purchase. Please reduce the number of selected products.',
      };
    }

    if (lowerError.includes('minimum') || lowerError.includes('at least')) {
      return {
        type: 'warning',
        title: 'No Products Selected',
        message: 'Please select at least one product to optimize.',
      };
    }
  }

  // Network errors
  if (lowerError.includes('network') || lowerError.includes('connection') || lowerError.includes('timeout')) {
    return {
      type: 'error',
      title: 'Connection Problem',
      message: 'Please check your internet connection and try again.',
      action: {
        label: 'Retry',
        onClick: () => window.location.reload()
      }
    };
  }

  // Server errors
  if (lowerError.includes('server') || lowerError.includes('internal')) {
    return {
      type: 'error',
      title: 'Server Error',
      message: 'Something went wrong on our end. Please try again in a few moments.',
      action: {
        label: 'Try Again',
        onClick: () => window.location.reload()
      }
    };
  }

  // Default error message
  return {
    type: 'error',
    title: 'Something Went Wrong',
    message: 'An unexpected error occurred. Please try again or contact support if the problem persists.',
    action: {
      label: 'Try Again',
      onClick: () => window.location.reload()
    }
  };
}

/**
 * Get success messages for billing actions
 */
export function getBillingSuccessMessage(action: string, details?: any): UserMessage {
  switch (action) {
    case 'subscription_created':
      return {
        type: 'success',
        title: 'Subscription Activated!',
        message: `Your ${details?.planName || 'subscription'} is now active. You can start optimizing your products right away.`,
        action: {
          label: 'Start Optimizing',
          onClick: () => window.location.href = '/app'
        }
      };

    case 'subscription_cancelled':
      return {
        type: 'info',
        title: 'Subscription Cancelled',
        message: 'Your subscription has been cancelled. You can still use the app until your current billing period ends.',
        action: {
          label: 'View Dashboard',
          onClick: () => window.location.href = '/app/billing'
        }
      };

    case 'purchase_created':
      return {
        type: 'success',
        title: 'Purchase Successful!',
        message: `You can now optimize ${details?.productCount || 'your selected'} products. The optimization will begin shortly.`,
        action: {
          label: 'View Products',
          onClick: () => window.location.href = '/app'
        }
      };

    case 'trial_started':
      return {
        type: 'success',
        title: 'Free Trial Started!',
        message: 'Your free trial is now active. Explore all features and optimize your products for free.',
        action: {
          label: 'Get Started',
          onClick: () => window.location.href = '/app'
        }
      };

    default:
      return {
        type: 'success',
        title: 'Success!',
        message: 'Your action was completed successfully.',
      };
  }
}

/**
 * Get loading messages for different actions
 */
export function getLoadingMessage(action: string): string {
  switch (action) {
    case 'create_subscription':
      return 'Setting up your subscription...';
    case 'cancel_subscription':
      return 'Cancelling your subscription...';
    case 'create_pay_per_use_purchase':
      return 'Processing your payment...';
    case 'loading_billing':
      return 'Loading your billing information...';
    case 'refreshing_data':
      return 'Refreshing your data...';
    default:
      return 'Processing...';
  }
}

/**
 * Format currency amounts for display
 */
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
}

/**
 * Format dates for display
 */
export function formatDate(date: string | Date | null | undefined): string {
  if (!date) return 'Not available';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch {
    return 'Invalid date';
  }
}

/**
 * Format relative time (e.g., "2 days ago")
 */
export function formatRelativeTime(date: string | Date | null | undefined): string {
  if (!date) return 'Unknown';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffMs = now.getTime() - dateObj.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    
    return `${Math.floor(diffDays / 365)} years ago`;
  } catch {
    return 'Unknown';
  }
}

/**
 * Get plan display information
 */
export function getPlanDisplayInfo(planId: string) {
  const planInfo = {
    monthly: {
      name: 'Monthly Plan',
      description: 'Perfect for getting started',
      badge: 'Popular',
      color: 'blue'
    },
    annual: {
      name: 'Annual Plan',
      description: 'Best value with 2 months free',
      badge: 'Best Value',
      color: 'green'
    },
    pay_per_use: {
      name: 'Pay-Per-Use',
      description: 'Only pay for what you optimize',
      badge: 'Flexible',
      color: 'purple'
    }
  };

  return planInfo[planId as keyof typeof planInfo] || {
    name: 'Unknown Plan',
    description: '',
    badge: '',
    color: 'gray'
  };
}
